import React, { useEffect, useRef } from 'react';

interface DiurnalLayerProps {
  skyMode: string;
}

interface Cloud {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  opacity: number;
  cloudNumber: number;
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = ({ skyMode }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  console.log('🌤️ CISCO: DiurnalLayer rendu avec skyMode:', skyMode);

  // Génération des nuages
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 15;
    
    // Nuages disponibles dans le dossier /Clouds/
    const availableCloudNumbers = [48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70];

    for (let i = 0; i < cloudCount; i++) {
      const cloudNumber = availableCloudNumbers[Math.floor(Math.random() * availableCloudNumbers.length)];
      
      clouds.push({
        id: i,
        x: -20 + Math.random() * 140, // Position X de -20% à 120%
        y: 5 + Math.random() * 40,    // Position Y de 5% à 45%
        size: 0.8 + Math.random() * 1.5, // Taille de 0.8x à 2.3x
        duration: 60 + Math.random() * 120, // Durée de 60s à 180s
        opacity: 0.7 + Math.random() * 0.3, // Opacité de 0.7 à 1.0
        cloudNumber: cloudNumber
      });
    }

    return clouds;
  };

  // Initialisation des nuages
  useEffect(() => {
    if (!containerRef.current) return;

    console.log('🌤️ CISCO: Initialisation des nuages pour skyMode:', skyMode);

    const clouds = generateClouds();
    containerRef.current.innerHTML = '';

    console.log(`☁️ CISCO: Génération de ${clouds.length} nuages`);

    clouds.forEach((cloud) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'cloud-element';
      
      cloudElement.style.cssText = `
        position: absolute;
        left: ${cloud.x}%;
        top: ${cloud.y}%;
        pointer-events: none;
        z-index: 8;
        opacity: ${cloud.opacity};
        transform: scale(${cloud.size});
        animation: cloud-move ${cloud.duration}s linear infinite;
      `;

      const imgElement = document.createElement('img');
      imgElement.src = `/Clouds/cloud_${cloud.cloudNumber}.png`;
      imgElement.alt = `Nuage ${cloud.cloudNumber}`;
      imgElement.style.cssText = `
        width: 120px;
        height: auto;
        display: block;
        filter: brightness(0.8) saturate(1.0);
      `;

      cloudElement.appendChild(imgElement);
      containerRef.current.appendChild(cloudElement);
      
      console.log(`✅ CISCO: Nuage ${cloud.cloudNumber} ajouté au DOM`);
    });

    // Ajouter les animations CSS
    if (!document.querySelector('#cloud-animations')) {
      const style = document.createElement('style');
      style.id = 'cloud-animations';
      style.textContent = `
        @keyframes cloud-move {
          0% {
            transform: translateX(-100px) scale(var(--size, 1));
          }
          100% {
            transform: translateX(calc(100vw + 100px)) scale(var(--size, 1));
          }
        }
        
        .cloud-element {
          will-change: transform;
        }
      `;
      document.head.appendChild(style);
      console.log('🌤️ CISCO: Animations CSS ajoutées');
    }

  }, [skyMode]);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{
        zIndex: 8
      }}
    />
  );
};

export default DiurnalLayer;
